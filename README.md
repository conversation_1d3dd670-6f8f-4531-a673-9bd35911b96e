# 自定义背景桌面应用程序

这是一个使用Python和Tkinter创建的桌面应用程序，具有美观的渐变背景和简洁的用户界面。

## 功能特点

- **自定义渐变背景**：从浅蓝色到深紫色的美观渐变效果
- **装饰性元素**：半透明圆形装饰增加视觉效果
- **响应式设计**：窗口大小改变时背景自动调整
- **用户交互界面**：
  - 左上角：功能按钮
  - 左下角：文本输入框
  - 右侧：输出显示区域

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ [显示信息]                                                   │
│                                                             │
│                                    ┌─────────────────────┐  │
│                                    │    输出显示：        │  │
│                                    │                     │  │
│                                    │  显示输入的内容      │  │
│                                    │  带时间戳           │  │
│                                    │                     │  │
│                                    │                     │  │
│                                    │                     │  │
│                                    │                     │  │
│                                    │                     │  │
│                                    │   [清空输出]        │  │
│                                    └─────────────────────┘  │
│                                                             │
│ ┌─────────────────┐                                         │
│ │  请输入信息：    │                                         │
│ │                │                                         │
│ │  [输入框]       │                                         │
│ │                │                                         │
│ └─────────────────┘                                         │
└─────────────────────────────────────────────────────────────┘
```

## 使用方法

1. **启动应用程序**：
   ```bash
   python main_app.py
   ```

2. **使用步骤**：
   - 在左下角的输入框中输入你想要显示的文本
   - 点击左上角的"显示信息"按钮
   - 输入的内容将显示在右侧的输出区域，并带有时间戳
   - 可以使用"清空输出"按钮清除输出区域的内容

## 系统要求

- Python 3.6 或更高版本
- Tkinter（通常随Python一起安装）

## 文件说明

- `main_app.py` - 主应用程序文件
- `generate_background.py` - 背景生成工具（可选）
- `README.md` - 说明文档

## 功能详解

### 主要功能
1. **文本输入与显示**：用户可以在输入框中输入任意文本，点击按钮后在输出区域显示
2. **时间戳记录**：每次显示内容时都会自动添加当前时间戳
3. **历史记录**：输出区域保留所有历史输入记录
4. **清空功能**：可以一键清空所有输出内容

### 界面特色
- **渐变背景**：使用Canvas绘制的蓝紫色渐变背景
- **装饰元素**：随机分布的半透明圆形装饰
- **响应式布局**：窗口大小改变时界面元素自动调整
- **现代化设计**：使用现代化的颜色搭配和字体

## 自定义选项

你可以通过修改代码来自定义：

1. **背景颜色**：修改 `draw_background()` 方法中的RGB值
2. **窗口大小**：修改 `self.root.geometry("800x600")` 中的尺寸
3. **按钮样式**：修改按钮的颜色、字体等属性
4. **装饰元素**：调整圆形装饰的数量、大小和位置

## 故障排除

如果遇到问题：

1. **程序无法启动**：确保已安装Python 3.6+
2. **界面显示异常**：尝试调整窗口大小或重启程序
3. **中文显示问题**：确保系统支持微软雅黑字体

## 扩展建议

可以考虑添加的功能：
- 保存输出内容到文件
- 支持富文本格式
- 添加更多背景主题
- 支持拖拽文件输入
- 添加快捷键支持

---

享受使用这个美观的桌面应用程序！
