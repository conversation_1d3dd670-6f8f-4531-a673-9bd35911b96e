#!/usr/bin/env python3
"""
使用tkinter创建简单的背景图片
"""

import tkinter as tk
from tkinter import Canvas
import math

def create_simple_background():
    """创建一个简单的背景图片文件"""
    print("正在创建背景图片...")

    # 创建一个临时窗口来生成背景
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 创建画布
    canvas = Canvas(root, width=800, height=600)

    # 创建渐变背景效果
    for i in range(600):
        # 计算颜色渐变
        ratio = i / 600
        r = int(135 + (147 - 135) * ratio)  # 从浅蓝到紫色的红色分量
        g = int(206 + (112 - 206) * ratio)  # 绿色分量
        b = int(250 + (219 - 250) * ratio)  # 蓝色分量

        color = f"#{r:02x}{g:02x}{b:02x}"
        canvas.create_line(0, i, 800, i, fill=color, width=1)

    # 添加一些装饰性圆形
    import random
    for _ in range(10):
        x = random.randint(50, 750)
        y = random.randint(50, 550)
        radius = random.randint(20, 60)

        # 创建半透明效果的圆形
        canvas.create_oval(x-radius, y-radius, x+radius, y+radius,
                          fill="#ffffff", stipple="gray25", outline="")

    # 保存为PostScript文件然后转换
    canvas.postscript(file="background.eps")

    root.destroy()
    print("背景图片创建完成")

if __name__ == "__main__":
    create_simple_background()
