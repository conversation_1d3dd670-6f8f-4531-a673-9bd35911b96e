#!/usr/bin/env python3
"""
带有自定义背景的桌面应用程序
功能：左上角按钮，左下角输入框，右边输出显示框
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import math

class CustomApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("自定义背景应用程序")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置最小窗口大小
        self.root.minsize(600, 400)
        
        self.setup_ui()
        
    def create_gradient_background(self):
        """创建渐变背景画布"""
        # 创建主画布作为背景
        self.bg_canvas = tk.Canvas(self.root, highlightthickness=0)
        self.bg_canvas.place(x=0, y=0, relwidth=1, relheight=1)
        
        # 绑定窗口大小改变事件
        self.root.bind('<Configure>', self.on_window_resize)
        
        # 初始绘制背景
        self.draw_background()
        
    def draw_background(self):
        """绘制渐变背景和装饰元素"""
        # 清除画布
        self.bg_canvas.delete("all")
        
        # 获取当前窗口大小
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        
        if width <= 1 or height <= 1:  # 窗口还未完全初始化
            width, height = 800, 600
        
        # 创建渐变背景
        for i in range(height):
            # 计算颜色渐变 - 从浅蓝到深紫
            ratio = i / height if height > 0 else 0
            r = int(135 + (75 - 135) * ratio)   # 红色分量
            g = int(206 + (0 - 206) * ratio)    # 绿色分量  
            b = int(250 + (130 - 250) * ratio)  # 蓝色分量
            
            # 确保颜色值在有效范围内
            r = max(0, min(255, r))
            g = max(0, min(255, g))
            b = max(0, min(255, b))
            
            color = f"#{r:02x}{g:02x}{b:02x}"
            self.bg_canvas.create_line(0, i, width, i, fill=color, width=1)
        
        # 添加装饰性圆形
        import random
        random.seed(42)  # 固定种子确保每次绘制相同
        for _ in range(8):
            x = random.randint(50, max(100, width-50))
            y = random.randint(50, max(100, height-50))
            radius = random.randint(30, 80)
            
            # 创建半透明圆形效果
            self.bg_canvas.create_oval(x-radius, y-radius, x+radius, y+radius, 
                                     fill="#ffffff", stipple="gray12", outline="")
        
    def on_window_resize(self, event):
        """窗口大小改变时重绘背景"""
        if event.widget == self.root:
            self.root.after_idle(self.draw_background)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建渐变背景
        self.create_gradient_background()
        
        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg='', highlightthickness=0)
        self.main_frame.place(relx=0, rely=0, relwidth=1, relheight=1)
        
        # 设置透明背景
        self.main_frame.configure(bg='')
        
        # 左上角按钮
        self.action_button = tk.Button(
            self.main_frame,
            text="显示信息",
            font=("微软雅黑", 12, "bold"),
            bg="#4CAF50",
            fg="white",
            activebackground="#45a049",
            activeforeground="white",
            relief="raised",
            bd=2,
            padx=20,
            pady=10,
            command=self.display_input_text
        )
        self.action_button.place(x=20, y=20)
        
        # 左下角输入框
        input_frame = tk.Frame(self.main_frame, bg="#ffffff", relief="raised", bd=2)
        input_frame.place(x=20, y=450, width=300, height=120)
        
        # 输入框标签
        input_label = tk.Label(
            input_frame,
            text="请输入信息：",
            font=("微软雅黑", 10),
            bg="#ffffff"
        )
        input_label.pack(pady=(5, 0))
        
        # 输入文本框
        self.input_text = tk.Text(
            input_frame,
            font=("微软雅黑", 10),
            wrap=tk.WORD,
            relief="sunken",
            bd=1,
            height=4
        )
        self.input_text.pack(padx=10, pady=(5, 10), fill=tk.BOTH, expand=True)
        
        # 右边输出显示框
        output_frame = tk.Frame(self.main_frame, bg="#ffffff", relief="raised", bd=2)
        output_frame.place(x=400, y=80, width=360, height=480)
        
        # 输出框标签
        output_label = tk.Label(
            output_frame,
            text="输出显示：",
            font=("微软雅黑", 12, "bold"),
            bg="#ffffff"
        )
        output_label.pack(pady=(10, 5))
        
        # 输出文本框（带滚动条）
        self.output_text = scrolledtext.ScrolledText(
            output_frame,
            font=("微软雅黑", 10),
            wrap=tk.WORD,
            relief="sunken",
            bd=1,
            state=tk.DISABLED  # 设置为只读
        )
        self.output_text.pack(padx=10, pady=(0, 10), fill=tk.BOTH, expand=True)
        
        # 清空按钮
        clear_button = tk.Button(
            output_frame,
            text="清空输出",
            font=("微软雅黑", 9),
            bg="#f44336",
            fg="white",
            activebackground="#da190b",
            activeforeground="white",
            command=self.clear_output
        )
        clear_button.pack(pady=(0, 10))
        
    def display_input_text(self):
        """显示输入框中的文本到输出框"""
        # 获取输入框内容
        input_content = self.input_text.get("1.0", tk.END).strip()
        
        if input_content:
            # 启用输出框编辑
            self.output_text.config(state=tk.NORMAL)
            
            # 添加时间戳
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 在输出框中添加内容
            self.output_text.insert(tk.END, f"[{timestamp}]\n")
            self.output_text.insert(tk.END, f"{input_content}\n")
            self.output_text.insert(tk.END, "-" * 40 + "\n\n")
            
            # 滚动到底部
            self.output_text.see(tk.END)
            
            # 禁用输出框编辑
            self.output_text.config(state=tk.DISABLED)
            
            # 清空输入框
            self.input_text.delete("1.0", tk.END)
        else:
            # 如果输入框为空，显示提示
            self.output_text.config(state=tk.NORMAL)
            self.output_text.insert(tk.END, "提示：请先在输入框中输入一些内容！\n\n")
            self.output_text.see(tk.END)
            self.output_text.config(state=tk.DISABLED)
    
    def clear_output(self):
        """清空输出框内容"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete("1.0", tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def run(self):
        """运行应用程序"""
        # 初始化时绘制背景
        self.root.after(100, self.draw_background)
        self.root.mainloop()

def main():
    """主函数"""
    app = CustomApp()
    app.run()

if __name__ == "__main__":
    main()
